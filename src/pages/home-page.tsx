import { Button } from '@/components/ui/button';
import { useState, useEffect } from 'react';
import { useLocation } from 'wouter';

export default function HomePage() {
  const [, setLocation] = useLocation();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulating content loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const handleGetStarted = () => {
    setLocation('/onboarding/domain');
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-4 lg:p-0">
      <div className="bg-white rounded-lg shadow-lg overflow-hidden max-w-4xl w-full flex flex-col md:flex-row">
        <div className="md:w-1/2 p-8 md:p-12 flex flex-col justify-center space-y-6">
          <div className="text-primary font-semibold text-xl">
            SEO<span className="text-secondary">45</span>
          </div>
          <h1 className="text-3xl sm:text-4xl font-bold text-gray-900">
            Welcome to SEO45 AI Blogger
          </h1>
          <p className="text-gray-600">
            Artificial Intelligence For Your Marketing Needs
          </p>

          <Button
            onClick={handleGetStarted}
            className="mt-6 max-w-xs"
            size="lg"
          >
            Let's Get Started
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 ml-2"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </Button>
        </div>
        <div className="md:w-1/2 bg-white p-8 md:p-0 flex items-center justify-center">
          <div className="w-full h-full relative">
            <svg
              className="w-full h-auto md:h-full md:w-auto"
              viewBox="0 0 800 600"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g
                opacity={isLoading ? 0.3 : 1}
                style={{ transition: 'opacity 0.5s ease-in-out' }}
              >
                {/* Abstract network visualization representing AI */}
                <path
                  d="M400 300 L450 350 L500 280 L550 400 L600 320"
                  stroke="#2563EB"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M400 300 L350 250 L300 320 L250 200 L200 280"
                  stroke="#2563EB"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M400 300 L450 250 L500 220 L550 180 L600 220"
                  stroke="#2563EB"
                  strokeWidth="2"
                  strokeLinecap="round"
                />
                <path
                  d="M400 300 L350 350 L300 380 L250 420 L200 380"
                  stroke="#2563EB"
                  strokeWidth="2"
                  strokeLinecap="round"
                />

                {/* Nodes */}
                <circle cx="400" cy="300" r="8" fill="#2563EB" />
                <circle cx="450" cy="350" r="6" fill="#2563EB" />
                <circle cx="500" cy="280" r="6" fill="#2563EB" />
                <circle cx="550" cy="400" r="6" fill="#2563EB" />
                <circle cx="600" cy="320" r="6" fill="#2563EB" />
                <circle cx="350" cy="250" r="6" fill="#2563EB" />
                <circle cx="300" cy="320" r="6" fill="#2563EB" />
                <circle cx="250" cy="200" r="6" fill="#2563EB" />
                <circle cx="200" cy="280" r="6" fill="#2563EB" />
                <circle cx="450" cy="250" r="6" fill="#2563EB" />
                <circle cx="500" cy="220" r="6" fill="#2563EB" />
                <circle cx="550" cy="180" r="6" fill="#2563EB" />
                <circle cx="600" cy="220" r="6" fill="#2563EB" />
                <circle cx="350" cy="350" r="6" fill="#2563EB" />
                <circle cx="300" cy="380" r="6" fill="#2563EB" />
                <circle cx="250" cy="420" r="6" fill="#2563EB" />
                <circle cx="200" cy="380" r="6" fill="#2563EB" />

                {/* Human profile silhouette */}
                <path
                  d="M350 150 C400 100, 450 100, 500 150 C520 170, 520 200, 500 230 C480 260, 450 280, 400 280 C350 280, 320 260, 300 230 C280 200, 280 170, 300 150 C320 130, 350 150, 350 150 Z"
                  stroke="#CCCCCC"
                  strokeWidth="1"
                  fill="none"
                />
              </g>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
}

import { DashboardPageWrapper } from '@/components/common';
import { PageHeading } from '@/components/common/headings';
import { SectionWrapper } from '@/components/common/section-wrapper';
import { adminService } from '@/services/admin';
import type { Stat, StatsState } from '@/types/admin/dashboard.types';
import {
  AlertCircle,
  CreditCard,
  Globe,
  Package,
  Receipt,
  Tag,
  Users,
} from 'lucide-react';
import { useEffect, useState } from 'react';
import QuickActions from './quick-action';
import { StatCard } from './stat-card';
import SystemInfo from './system-info';

// Main Component
const AdminDashboardPage: React.FC = () => {
  const [stats, setStats] = useState<StatsState>({
    users: 0,
    subscriptions: 0,
    coupons: 0,
    plans: 0,
    websites: 0,
    transactions: 0,
  });
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const [users, subscriptions, coupons, plans] = await Promise.all([
          adminService.getAllUsers(),
          adminService.getAllSubscriptions(),
          adminService.getAllCoupons(),
          adminService.getAllPlans(),
        ]);

        setStats({
          users: users.length,
          subscriptions: subscriptions.length,
          coupons: coupons.length,
          plans: plans.length,
          websites: 67,
          transactions: 345,
        });
        setError(null);
      } catch (err) {
        setError('Failed to fetch dashboard data');
        console.error(err);
        setStats({
          users: 245,
          subscriptions: 128,
          plans: 4,
          coupons: 12,
          websites: 67,
          transactions: 345,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const dashboardStats: Stat[] = [
    {
      title: 'Users',
      value: stats.users,
      href: '/admin/users',
      icon: <Users className="h-4 w-4" />,
      color: 'bg-blue-500/20 text-blue-500',
      description: 'Total registered users',
    },
    {
      title: 'Subscriptions',
      value: stats.subscriptions,
      href: '/admin/subscriptions',
      icon: <CreditCard className="h-4 w-4" />,
      color: 'bg-green-500/20 text-green-500',
      description: 'Active subscriptions',
    },
    {
      title: 'Plans',
      value: stats.plans,
      href: '/admin/plans',
      icon: <Package className="h-4 w-4" />,
      color: 'bg-purple-500/20 text-purple-500',
      description: 'Available pricing plans',
    },
    {
      title: 'Coupons',
      value: stats.coupons,
      href: '/admin/coupons',
      icon: <Tag className="h-4 w-4" />,
      color: 'bg-yellow-500/20 text-yellow-500',
      description: 'Active discount coupons',
    },
    {
      title: 'Websites',
      value: stats.websites,
      href: '/admin/websites',
      icon: <Globe className="h-4 w-4" />,
      color: 'bg-pink-500/20 text-pink-500',
      description: 'Managed websites',
    },
    {
      title: 'Transactions',
      value: stats.transactions,
      href: '/admin/transactions',
      icon: <Receipt className="h-4 w-4" />,
      color: 'bg-indigo-500/20 text-indigo-500',
      description: 'Total payment transactions',
    },
  ];

  return (
    <DashboardPageWrapper>
      <PageHeading
        title="Dashboard"
        description="Overview of your application statistics and quick actions."
      />

      {error && (
        <SectionWrapper spacing="sm">
          <div className="bg-destructive/15 text-destructive px-4 py-3 rounded-md flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            {error}
          </div>
        </SectionWrapper>
      )}

      {/* Stats Cards */}
      <SectionWrapper spacing="md">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {dashboardStats.map(stat => (
            <StatCard key={stat.title} {...stat} loading={loading} />
          ))}
        </div>
      </SectionWrapper>

      <QuickActions />

      <SystemInfo />
    </DashboardPageWrapper>
  );
};

export default AdminDashboardPage;

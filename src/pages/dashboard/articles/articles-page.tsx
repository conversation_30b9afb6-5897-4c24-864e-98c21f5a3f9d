import { DataTable } from '@/components/data-table/data-table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { getArticles } from '@/services/article/article-service';
import { useAuth } from '@/stores';
import { Article } from '@/types/article';
import { useQuery } from '@tanstack/react-query';
import { ColumnDef } from '@tanstack/react-table';
import {
  AlertCircle,
  Calendar,
  Eye,
  Grid3X3,
  List,
  RefreshCw,
} from 'lucide-react';
import { useState } from 'react';

type ViewMode = 'cards' | 'table';

export default function ArticlesPage() {
  const { user } = useAuth();
  const [viewMode, setViewMode] = useState<ViewMode>('cards');

  // Fetch articles from Google Apps Script
  const {
    data: allArticles = [],
    isLoading: articlesLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['articles', user?.id],
    queryFn: () => getArticles(user?.id || ''),
    enabled: !!user,
    retry: 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Table columns definition
  const columns: ColumnDef<Article>[] = [
    {
      accessorKey: 'featured_image',
      header: 'Image',
      cell: ({ row }) => {
        const article = row.original;
        return (
          <div className="w-16 h-16 bg-muted rounded-lg overflow-hidden">
            {article.featured_image ? (
              <img
                src={article.featured_image}
                alt={article.title}
                className="w-full h-full object-cover"
                onError={e => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-muted-foreground text-xs">
                No Image
              </div>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'title',
      header: 'Title',
      cell: ({ row }) => {
        const article = row.original;
        return (
          <div className="max-w-sm">
            <p className="font-medium text-sm">{article.title}</p>
            {(article.excerpt || article.content) && (
              <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                {article.excerpt ||
                  (article.content
                    ? article.content
                        .replace(/<[^>]*>/g, '')
                        .substring(0, 100) + '...'
                    : '')}
              </p>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: 'status',
      header: 'Status',
      cell: ({ row }) => {
        const status = row.getValue('status') as string;
        return (
          <Badge
            variant={
              status === 'published'
                ? 'default'
                : status === 'draft'
                  ? 'secondary'
                  : 'outline'
            }
            className="text-xs"
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'published_at',
      header: 'Published Date',
      cell: ({ row }) => {
        const date = row.getValue('published_at') as string;
        return date ? (
          <div className="flex items-center gap-1 text-sm">
            <Calendar className="w-3 h-3" />
            {new Date(date).toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
            })}
          </div>
        ) : (
          <span className="text-muted-foreground text-sm">-</span>
        );
      },
    },
    {
      accessorKey: 'views',
      header: 'Views',
      cell: ({ row }) => {
        const views = row.getValue('views') as number | undefined;
        return (
          <div className="flex items-center gap-1 text-sm">
            <Eye className="w-3 h-3" />
            {(views || 0).toLocaleString()}
          </div>
        );
      },
    },
    {
      accessorKey: 'seo_score',
      header: 'SEO Score',
      cell: ({ row }) => {
        const score = row.getValue('seo_score') as number | undefined;
        return score ? (
          <span className="text-sm font-medium">{score}%</span>
        ) : (
          <span className="text-muted-foreground text-sm">-</span>
        );
      },
    },
  ];

  // Show error state if there's an error
  if (error && !articlesLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-3xl font-bold text-foreground mb-1">Articles</h2>
          <p className="text-muted-foreground text-base">
            View all your automated blog articles
          </p>
        </div>

        <Card className="shadow-lg border-none bg-white dark:bg-zinc-900">
          <CardContent className="p-8 text-center">
            <AlertCircle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">Unable to load articles</CardTitle>
              <CardDescription className="text-sm">
                {error.message ||
                  'There was a problem connecting to your Google Apps Script. Please check your setup.'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => refetch()} className="mt-4">
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
            </CardContent>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with view toggle */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-3xl font-bold text-foreground mb-1">Articles</h2>
          <p className="text-muted-foreground text-base">
            View all your automated blog articles ({allArticles.length} total)
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'cards' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('cards')}
          >
            <Grid3X3 className="mr-2 h-4 w-4" />
            Cards
          </Button>
          <Button
            variant={viewMode === 'table' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('table')}
          >
            <List className="mr-2 h-4 w-4" />
            Table
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            disabled={articlesLoading}
          >
            <RefreshCw
              className={`mr-2 h-4 w-4 ${articlesLoading ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
        </div>
      </div>

      {/* Content */}
      {viewMode === 'table' ? (
        <Card className="shadow-lg border-none bg-white dark:bg-zinc-900">
          <CardContent className="p-6">
            <DataTable
              columns={columns}
              data={allArticles}
              searchKey="title"
              filterableColumns={[
                {
                  id: 'status',
                  title: 'Status',
                  options: [
                    { label: 'Published', value: 'published' },
                    { label: 'Draft', value: 'draft' },
                    { label: 'Scheduled', value: 'scheduled' },
                  ],
                },
              ]}
            />
          </CardContent>
        </Card>
      ) : (
        <ArticleCardsView articles={allArticles} loading={articlesLoading} />
      )}
    </div>
  );
}

// Cards View Component
function ArticleCardsView({
  articles,
  loading,
}: {
  articles: Article[];
  loading: boolean;
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const articlesPerPage = 12;

  const totalPages = Math.ceil(articles.length / articlesPerPage);
  const startIndex = (currentPage - 1) * articlesPerPage;
  const endIndex = startIndex + articlesPerPage;
  const currentArticles = articles.slice(startIndex, endIndex);

  return (
    <>
      <Card className="shadow-lg border-none bg-white dark:bg-zinc-900">
        <CardContent className="p-6">
          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="w-full h-48 bg-muted rounded-lg mb-3"></div>
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-muted rounded w-1/4"></div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {currentArticles.map(article => (
                <div
                  key={article.id}
                  className="bg-card border rounded-lg overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
                >
                  {/* Article Image */}
                  <div className="w-full h-48 bg-muted overflow-hidden">
                    {article.featured_image ? (
                      <img
                        src={article.featured_image}
                        alt={article.title}
                        className="w-full h-full object-cover"
                        onError={e => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                        No Image
                      </div>
                    )}
                  </div>

                  {/* Article Content */}
                  <div className="p-4">
                    <h3 className="font-semibold text-sm mb-2 line-clamp-2">
                      {article.title}
                    </h3>

                    {/* Description/Excerpt */}
                    {(article.excerpt || article.content) && (
                      <p className="text-muted-foreground text-xs line-clamp-3 mb-3">
                        {article.excerpt ||
                          (article.content
                            ? article.content
                                .replace(/<[^>]*>/g, '')
                                .substring(0, 120) + '...'
                            : '')}
                      </p>
                    )}

                    {/* Meta Information */}
                    <div className="flex items-center justify-between text-xs text-muted-foreground mb-2">
                      {article.published_at && (
                        <div className="flex items-center gap-1">
                          <Calendar className="w-3 h-3" />
                          <span>
                            {new Date(article.published_at).toLocaleDateString(
                              'en-US',
                              {
                                month: 'short',
                                day: 'numeric',
                              }
                            )}
                          </span>
                        </div>
                      )}

                      {article.views !== undefined && (
                        <div className="flex items-center gap-1">
                          <Eye className="w-3 h-3" />
                          <span>{article.views.toLocaleString()}</span>
                        </div>
                      )}
                    </div>

                    <Badge
                      variant={
                        article.status === 'published'
                          ? 'default'
                          : article.status === 'draft'
                            ? 'secondary'
                            : 'outline'
                      }
                      className="text-xs"
                    >
                      {article.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}

          {articles.length === 0 && !loading && (
            <div className="text-center py-16">
              <CardHeader>
                <CardTitle>No articles found</CardTitle>
                <CardDescription>
                  Your automated articles will appear here once they're
                  published
                </CardDescription>
              </CardHeader>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination for Cards View */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>

          <div className="flex items-center gap-1">
            {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
              <Button
                key={page}
                variant={currentPage === page ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCurrentPage(page)}
                className="w-8 h-8 p-0"
              >
                {page}
              </Button>
            ))}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() =>
              setCurrentPage(prev => Math.min(prev + 1, totalPages))
            }
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </>
  );
}

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { WebsiteCard } from '@/components/website/website-card';
import { Website } from '@/supabase/types';
import { Plus } from 'lucide-react';

interface WebsiteGridProps {
  websites: Website[];
  loading?: boolean;
  onAddWebsite: () => void;
}

export const WebsiteGrid: React.FC<WebsiteGridProps> = ({
  websites,
  loading,
  onAddWebsite,
}) => (
  <div className="space-y-4 md:space-y-6">
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h2 className="text-2xl md:text-3xl font-bold text-foreground mb-2">
          Your Websites
        </h2>
        <p className="text-muted-foreground text-sm md:text-base">
          Manage your websites and subscriptions
        </p>
      </div>
      <Button onClick={onAddWebsite} className="w-full sm:w-auto">
        <Plus className="mr-2 h-4 w-4" />
        Add Website
      </Button>
    </div>

    {loading ? (
      <div className="grid grid-cols-1 gap-4 md:gap-6">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3 flex-1">
                  <div className="w-9 h-9 bg-muted rounded-lg"></div>
                  <div className="flex-1">
                    <div className="h-5 bg-muted rounded w-1/2 mb-2"></div>
                    <div className="h-4 bg-muted rounded w-1/3"></div>
                  </div>
                </div>
                <div className="h-6 bg-muted rounded w-20"></div>
              </div>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="h-16 bg-muted rounded-lg"></div>
                <div className="h-16 bg-muted rounded-lg"></div>
              </div>
              <div className="space-y-3">
                <div className="h-4 bg-muted rounded"></div>
                <div className="h-4 bg-muted rounded"></div>
                <div className="h-4 bg-muted rounded w-3/4"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    ) : (
      <div className="grid grid-cols-1 gap-4 md:gap-6">
        {websites.map((website: Website) => (
          <WebsiteCard
            key={website.id}
            website={website}
            showArticleCount={false}
          />
        ))}
      </div>
    )}
  </div>
);

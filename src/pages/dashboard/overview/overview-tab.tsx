import { SEO45Hero } from '@/components/dashboard/seo45-hero';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { WebsiteCard } from '@/components/website/website-card';
import { Subscription, Website } from '@/supabase/types';
import { Globe } from 'lucide-react';

interface OverviewTabProps {
  websites: Website[];
  subscriptions: Subscription[];
  loading?: boolean;
  onAddWebsite: () => void;
}

export const OverviewTab: React.FC<OverviewTabProps> = ({
  websites,
  subscriptions,
  loading,
  onAddWebsite,
}) => {
  return (
    <div className="space-y-6">
      {/* SEO45 Hero */}
      <SEO45Hero
        websiteCount={websites.length}
        articleCount={websites.reduce(
          (total, website) => total + (website.website_description ? 1 : 0),
          0
        )}
        onAddWebsite={onAddWebsite}
      />

      {/* Websites Section */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-foreground">
            Your Websites
          </h2>
          <Button
            onClick={onAddWebsite}
            variant="default"
            size="sm"
            className="font-medium"
          >
            Add Website
          </Button>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 gap-4">
            {[1, 2, 3].map(i => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3 flex-1">
                      <div className="w-9 h-9 bg-muted rounded-lg"></div>
                      <div className="flex-1">
                        <div className="h-5 bg-muted rounded w-1/2 mb-2"></div>
                        <div className="h-4 bg-muted rounded w-1/3"></div>
                      </div>
                    </div>
                    <div className="h-6 bg-muted rounded w-20"></div>
                  </div>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="h-16 bg-muted rounded-lg"></div>
                    <div className="h-16 bg-muted rounded-lg"></div>
                  </div>
                  <div className="space-y-3">
                    <div className="h-4 bg-muted rounded"></div>
                    <div className="h-4 bg-muted rounded"></div>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : websites.length === 0 ? (
          <Card className="text-center py-12">
            <CardContent>
              <Globe className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                No websites yet
              </h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                Add your first website to start automated content creation and
                SEO optimization
              </p>
              <Button onClick={onAddWebsite} size="lg" className="font-medium">
                Add Your First Website
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 gap-4">
            {websites.map(website => (
              <WebsiteCard
                key={website.id}
                website={website}
                subscriptions={subscriptions}
                showArticleCount={true}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

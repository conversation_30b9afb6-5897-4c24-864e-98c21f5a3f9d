import { lazy } from 'react';
import { adminRoutes } from './admin/config';
import DashboardGuard from './components/dashboard-guard';
import { RouteConfig } from './types';

// Lazy load pages
const AuthPage = lazy(() => import('@/pages/auth/auth-page'));
const OnboardingPage = lazy(() => import('@/pages/onboarding/onboarding-page'));
const NotFoundPage = lazy(() => import('@/pages/not-found'));

// Dashboard pages
const OverviewPage = lazy(
  () => import('@/pages/dashboard/overview/overview-page')
);
const WebsitesPage = lazy(
  () => import('@/pages/dashboard/websites/websites-page')
);
const ArticlesPage = lazy(
  () => import('@/pages/dashboard/articles/articles-page')
);

const SubscriptionsPage = lazy(
  () => import('@/pages/dashboard/subscription/subscriptions-page')
);

// Route configurations
export const routes: RouteConfig[] = [
  {
    path: '/',
    component: DashboardGuard,
    protected: true,
    index: true,
    layout: 'none',
  },
  {
    path: '/dashboard',
    component: OverviewPage,
    protected: true,
    layout: 'dashboard',
  },

  {
    path: '/dashboard/websites',
    component: WebsitesPage,
    protected: true,
    layout: 'dashboard',
  },
  {
    path: '/dashboard/articles',
    component: ArticlesPage,
    protected: true,
    layout: 'dashboard',
  },

  {
    path: '/dashboard/subscriptions',
    component: SubscriptionsPage,
    protected: true,
    layout: 'dashboard',
  },
  {
    path: '/onboarding',
    component: OnboardingPage,
    protected: true,
    layout: 'root',
  },
  {
    path: '/auth',
    component: AuthPage,
    protected: false,
    layout: 'root',
  },
  ...adminRoutes,
];

export { NotFoundPage };

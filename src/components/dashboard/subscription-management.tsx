import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { getAllPlans } from '@/services/plan/plan-service';
import {
  cancelSubscription,
  createSubscription,
  getSubscriptionStatusDetails,
  getWebsitesWithSubscriptions,
  reactivateSubscription,
} from '@/services/subscription/subscription-service';
import { useAuth } from '@/stores';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import {
  AlertTriangle,
  Calendar,
  Check,
  CreditCard,
  Crown,
  Star,
  X,
  Zap,
} from 'lucide-react';
import { useState } from 'react';

export default function SubscriptionManagement() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [loadingAction, setLoadingAction] = useState<string | null>(null);

  // Fetch user's websites with subscription data
  const {
    data: websites = [],
    isLoading: websitesLoading,
    error: websitesError,
  } = useQuery({
    queryKey: ['websites-with-subscriptions', user?.id],
    queryFn: () => {
      if (!user?.id) throw new Error('User not authenticated');
      return getWebsitesWithSubscriptions(user.id);
    },
    enabled: !!user?.id,
  });

  // Fetch available plans
  const {
    data: plans = [],
    isLoading: plansLoading,
    error: plansError,
  } = useQuery({
    queryKey: ['plans'],
    queryFn: getAllPlans,
  });

  // Mutation for creating subscription
  const createSubscriptionMutation = useMutation({
    mutationFn: async ({ planId }: { planId: number }) => {
      if (!user) throw new Error('User not authenticated');

      const now = new Date();
      const periodStart = now.toISOString();
      const periodEnd = new Date(
        now.getTime() + 30 * 24 * 60 * 60 * 1000
      ).toISOString(); // 30 days from now

      return createSubscription({
        user_id: user.id,
        plan_id: planId,
        status: 'active',
        current_period_start: periodStart,
        current_period_end: periodEnd,
      });
    },
    onSuccess: () => {
      toast({
        title: 'Subscription Created!',
        description: 'Your subscription has been activated successfully.',
      });
      queryClient.invalidateQueries({
        queryKey: ['websites-with-subscriptions'],
      });
      setLoadingAction(null);
    },
    onError: error => {
      toast({
        title: 'Subscription Failed',
        description: error.message,
        variant: 'destructive',
      });
      setLoadingAction(null);
    },
  });

  // Mutation for canceling subscription
  const cancelSubscriptionMutation = useMutation({
    mutationFn: cancelSubscription,
    onSuccess: () => {
      toast({
        title: 'Subscription Cancelled',
        description: 'Your subscription will end at the current period.',
      });
      queryClient.invalidateQueries({
        queryKey: ['websites-with-subscriptions'],
      });
      setLoadingAction(null);
    },
    onError: error => {
      toast({
        title: 'Cancellation Failed',
        description: error.message,
        variant: 'destructive',
      });
      setLoadingAction(null);
    },
  });

  // Mutation for reactivating subscription
  const reactivateSubscriptionMutation = useMutation({
    mutationFn: reactivateSubscription,
    onSuccess: () => {
      toast({
        title: 'Subscription Reactivated',
        description: 'Your subscription has been reactivated successfully.',
      });
      queryClient.invalidateQueries({
        queryKey: ['websites-with-subscriptions'],
      });
      setLoadingAction(null);
    },
    onError: error => {
      toast({
        title: 'Reactivation Failed',
        description: error.message,
        variant: 'destructive',
      });
      setLoadingAction(null);
    },
  });

  const handleSubscribe = async (planId: number) => {
    setLoadingAction(`subscribe-${planId}`);
    createSubscriptionMutation.mutate({ planId });
  };

  const handleCancelSubscription = async (subscriptionId: number) => {
    setLoadingAction(`cancel-${subscriptionId}`);
    cancelSubscriptionMutation.mutate(subscriptionId);
  };

  const handleReactivateSubscription = async (subscriptionId: number) => {
    setLoadingAction(`reactivate-${subscriptionId}`);
    reactivateSubscriptionMutation.mutate(subscriptionId);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const getPopularPlanId = () => {
    if (plans.length === 0) return null;
    // Assume the middle-priced plan is most popular
    const sortedPlans = [...plans].sort((a, b) => a.price - b.price);
    return sortedPlans[Math.floor(sortedPlans.length / 2)]?.id;
  };

  const hasActiveSubscription = websites.some(
    website => website.subscription && website.subscription.status === 'active'
  );
  const userSubscription = websites.find(
    website => website.subscription
  )?.subscription;
  const subscriptionStatus = getSubscriptionStatusDetails(userSubscription);
  const popularPlanId = getPopularPlanId();

  if (websitesLoading || plansLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <Skeleton className="h-8 w-64 mx-auto mb-2" />
          <Skeleton className="h-4 w-96 mx-auto" />
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map(i => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-32 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (websitesError || plansError) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <AlertTriangle className="mx-auto h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            Error Loading Data
          </h3>
          <p className="text-muted-foreground mb-4">
            {websitesError?.message || plansError?.message}
          </p>
          <Button
            onClick={() => {
              queryClient.invalidateQueries({
                queryKey: ['websites-with-subscriptions'],
              });
              queryClient.invalidateQueries({ queryKey: ['plans'] });
            }}
          >
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-foreground mb-2">
          Subscription Management
        </h2>
        <p className="text-muted-foreground">
          {hasActiveSubscription
            ? 'Manage your active subscription'
            : 'Choose the perfect plan for your content strategy'}
        </p>
      </div>

      {/* Current Subscription Status */}
      {userSubscription && (
        <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl flex items-center gap-2">
                  <Crown className="h-5 w-5 text-primary" />
                  Current Subscription
                </CardTitle>
                <p className="text-muted-foreground mt-1">
                  {userSubscription.plans.name} Plan
                </p>
              </div>
              <Badge variant={subscriptionStatus.statusColor}>
                {subscriptionStatus.statusText}
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-background/50 rounded-lg">
                <CreditCard className="h-6 w-6 mx-auto mb-2 text-primary" />
                <p className="text-sm text-muted-foreground">Monthly Cost</p>
                <p className="text-lg font-bold">
                  {formatPrice(
                    userSubscription.plans.discounted_price ||
                      userSubscription.plans.price
                  )}
                </p>
              </div>
              <div className="text-center p-4 bg-background/50 rounded-lg">
                <Calendar className="h-6 w-6 mx-auto mb-2 text-primary" />
                <p className="text-sm text-muted-foreground">Next Billing</p>
                <p className="text-lg font-bold">
                  {formatDate(userSubscription.current_period_end)}
                </p>
              </div>
              <div className="text-center p-4 bg-background/50 rounded-lg">
                <Zap className="h-6 w-6 mx-auto mb-2 text-primary" />
                <p className="text-sm text-muted-foreground">Status</p>
                <p className="text-lg font-bold capitalize">
                  {userSubscription.cancel_at_period_end
                    ? 'Ending Soon'
                    : 'Active'}
                </p>
              </div>
            </div>

            {userSubscription.cancel_at_period_end && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center text-yellow-800">
                  <AlertTriangle className="mr-2 h-5 w-5" />
                  <span className="font-medium">Subscription Ending</span>
                </div>
                <p className="text-yellow-700 mt-1 text-sm">
                  Your subscription will end on{' '}
                  {formatDate(userSubscription.current_period_end)}. Reactivate
                  to continue your service.
                </p>
              </div>
            )}

            <div className="flex gap-3">
              {userSubscription.cancel_at_period_end ? (
                <Button
                  onClick={() =>
                    handleReactivateSubscription(userSubscription.id)
                  }
                  disabled={
                    loadingAction === `reactivate-${userSubscription.id}`
                  }
                  className="flex-1"
                >
                  {loadingAction === `reactivate-${userSubscription.id}` ? (
                    'Reactivating...'
                  ) : (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      Reactivate Subscription
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  variant="outline"
                  onClick={() => handleCancelSubscription(userSubscription.id)}
                  disabled={loadingAction === `cancel-${userSubscription.id}`}
                  className="flex-1"
                >
                  {loadingAction === `cancel-${userSubscription.id}` ? (
                    'Cancelling...'
                  ) : (
                    <>
                      <X className="mr-2 h-4 w-4" />
                      Cancel Subscription
                    </>
                  )}
                </Button>
              )}
              <Button variant="outline">
                <CreditCard className="mr-2 h-4 w-4" />
                Update Payment
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Available Plans */}
      {!hasActiveSubscription && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {plans.map(plan => (
            <Card
              key={plan.id}
              className={`relative ${
                plan.id === popularPlanId
                  ? 'border-primary shadow-lg scale-105'
                  : ''
              }`}
            >
              {plan.id === popularPlanId && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-primary text-primary-foreground">
                    <Star className="mr-1 h-3 w-3" />
                    Most Popular
                  </Badge>
                </div>
              )}
              <CardHeader className="text-center pb-4">
                <CardTitle className="text-lg">{plan.name}</CardTitle>
                <div className="text-3xl font-bold text-primary">
                  {formatPrice(plan.discounted_price || plan.price)}
                  <span className="text-sm font-normal text-muted-foreground">
                    /{plan.interval}
                  </span>
                </div>
                {plan.discounted_price && (
                  <p className="text-sm text-muted-foreground line-through">
                    {formatPrice(plan.price)}
                  </p>
                )}
                {plan.description && (
                  <p className="text-sm text-muted-foreground mt-2">
                    {plan.description}
                  </p>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                {plan.features && (
                  <ul className="space-y-2">
                    {(Array.isArray(plan.features)
                      ? plan.features
                      : typeof plan.features === 'string'
                        ? JSON.parse(plan.features)
                        : []
                    ).map((feature: string, index: number) => (
                      <li key={index} className="flex items-center text-sm">
                        <Check className="mr-2 h-4 w-4 text-green-500 flex-shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                )}
                <Button
                  className="w-full"
                  variant={plan.id === popularPlanId ? 'default' : 'outline'}
                  onClick={() => handleSubscribe(plan.id)}
                  disabled={loadingAction === `subscribe-${plan.id}`}
                >
                  {loadingAction === `subscribe-${plan.id}` ? (
                    'Processing...'
                  ) : (
                    <>
                      <Crown className="mr-2 h-4 w-4" />
                      Subscribe to {plan.name}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Websites List */}
      {websites.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Your Websites</CardTitle>
            <p className="text-muted-foreground">
              Websites covered by your subscription
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {websites.map(website => (
                <Card key={website.id} className="border">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-medium">{website.domain_name}</h4>
                        <p className="text-sm text-muted-foreground">
                          {website.website_url}
                        </p>
                      </div>
                      <Badge
                        variant={
                          website.status === 'active' ? 'default' : 'secondary'
                        }
                      >
                        {website.status}
                      </Badge>
                    </div>
                    {website.website_description && (
                      <p className="text-sm text-muted-foreground">
                        {website.website_description}
                      </p>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {websites.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Crown className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              No websites yet
            </h3>
            <p className="text-muted-foreground mb-4">
              Add your first website to get started with our content generation
              service.
            </p>
            <Button>Add Website</Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

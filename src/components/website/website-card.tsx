import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Subscription, Website } from '@/supabase/types';
import { format, formatDistanceToNow } from 'date-fns';
import {
  Calendar,
  Clock,
  ExternalLink,
  FileText,
  Globe,
  Server,
  Shield,
  Tag,
} from 'lucide-react';

interface WebsiteCardProps {
  website: Website;
  subscriptions?: Subscription[];
  showArticleCount?: boolean;
  className?: string;
}

export const WebsiteCard: React.FC<WebsiteCardProps> = ({
  website,
  subscriptions = [],
  showArticleCount = true,
  className = '',
}) => {
  const activeSubscription = subscriptions.find(sub => sub.status === 'active');

  const getStatusColor = (status: string | null) => {
    switch (status) {
      case 'active':
        return 'bg-green-500/20 text-green-700 border-green-500/30';
      case 'inactive':
        return 'bg-red-500/20 text-red-700 border-red-500/30';
      case 'maintenance':
        return 'bg-yellow-500/20 text-yellow-700 border-yellow-500/30';
      case 'setup-pending':
        return 'bg-blue-500/20 text-blue-700 border-blue-500/30';
      default:
        return 'bg-gray-500/20 text-gray-700 border-gray-500/30';
    }
  };

  // Generate random article count for demo (10-50)
  const getArticleCount = (_websiteId: number) => {
    return Math.floor(Math.random() * 40) + 10;
  };

  const articleCount = getArticleCount(website.id);

  return (
    <Card
      className={`group hover:shadow-lg transition-all duration-200 hover:border-blue-200 ${className}`}
    >
      <CardContent className="p-6">
        {/* Header Section */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 bg-blue-50 rounded-lg">
                <Globe className="h-5 w-5 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="font-semibold text-lg text-foreground truncate">
                  {website.domain_name}
                </h3>
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <ExternalLink className="h-3 w-3" />
                  <span className="truncate">{website.website_url}</span>
                </div>
              </div>
            </div>
            {website.website_description && (
              <p className="text-sm text-muted-foreground line-clamp-2 mt-2">
                {website.website_description}
              </p>
            )}
          </div>
          <Badge className={getStatusColor(website.status)}>
            {website.status || 'setup-pending'}
          </Badge>
        </div>

        {/* Main Info Grid */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          {/* Article Count */}
          {showArticleCount && (
            <div className="flex items-center gap-2 p-3 bg-green-50 rounded-lg">
              <FileText className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-lg font-semibold text-green-700">
                  {articleCount}
                </div>
                <div className="text-xs text-green-600">Articles</div>
              </div>
            </div>
          )}

          {/* SSL Status */}
          <div className="flex items-center gap-2 p-3 bg-purple-50 rounded-lg">
            <Shield className="h-4 w-4 text-purple-600" />
            <div>
              <div className="text-lg font-semibold text-purple-700">
                {website.ssl_enabled ? 'Yes' : 'No'}
              </div>
              <div className="text-xs text-purple-600">SSL Enabled</div>
            </div>
          </div>

          {/* Hosting Provider */}
          {website.hosting_provider && (
            <div className="flex items-center gap-2 p-3 bg-orange-50 rounded-lg col-span-2">
              <Server className="h-4 w-4 text-orange-600" />
              <div>
                <div className="text-sm font-medium text-orange-700">
                  {website.hosting_provider}
                </div>
                <div className="text-xs text-orange-600">Hosting Provider</div>
              </div>
            </div>
          )}
        </div>

        {/* Additional Info */}
        <div className="space-y-3">
          {/* Website Niche */}
          {website.website_niche && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Tag className="h-4 w-4" />
                <span>Niche</span>
              </div>
              <span className="text-sm font-medium bg-blue-50 text-blue-700 px-2 py-1 rounded">
                {website.website_niche}
              </span>
            </div>
          )}

          {/* Subscription Info */}
          <div className="pt-3 border-t border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-muted-foreground">
                Subscription
              </span>
              <Badge variant={activeSubscription ? 'default' : 'secondary'}>
                {activeSubscription ? 'Active' : 'None'}
              </Badge>
            </div>
            {activeSubscription && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <Calendar className="h-3 w-3" />
                <span>
                  Until{' '}
                  {format(
                    new Date(activeSubscription.current_period_end),
                    'MMM dd, yyyy'
                  )}
                </span>
              </div>
            )}
          </div>

          {/* Created Date */}
          <div className="flex items-center gap-1 text-xs text-muted-foreground pt-2">
            <Clock className="h-3 w-3" />
            <span>
              Added{' '}
              {formatDistanceToNow(new Date(website.created_at || ''), {
                addSuffix: true,
              })}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
